/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/kaiban-super-agent/page";
exports.ids = ["app/kaiban-super-agent/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkaiban-super-agent%2Fpage&page=%2Fkaiban-super-agent%2Fpage&appPaths=%2Fkaiban-super-agent%2Fpage&pagePath=private-next-app-dir%2Fkaiban-super-agent%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkaiban-super-agent%2Fpage&page=%2Fkaiban-super-agent%2Fpage&appPaths=%2Fkaiban-super-agent%2Fpage&pagePath=private-next-app-dir%2Fkaiban-super-agent%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/kaiban-super-agent/page.tsx */ \"(rsc)/./src/app/kaiban-super-agent/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'kaiban-super-agent',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/kaiban-super-agent/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/kaiban-super-agent/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/kaiban-super-agent/page\",\n        pathname: \"/kaiban-super-agent\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkaiban-super-agent%2Fpage&page=%2Fkaiban-super-agent%2Fpage&appPaths=%2Fkaiban-super-agent%2Fpage&pagePath=private-next-app-dir%2Fkaiban-super-agent%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(rsc)/./src/contexts/SettingsContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(rsc)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FKaibanSuperAgent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FKaibanSuperAgent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/KaibanSuperAgent.tsx */ \"(rsc)/./src/components/KaibanSuperAgent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGY29tcG9uZW50cyUyRkthaWJhblN1cGVyQWdlbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWtLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL0thaWJhblN1cGVyQWdlbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FKaibanSuperAgent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fc4f0949cb4b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZjNGYwOTQ5Y2I0YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/kaiban-super-agent/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/kaiban-super-agent/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KaibanSuperAgentPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_KaibanSuperAgent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/KaibanSuperAgent */ \"(rsc)/./src/components/KaibanSuperAgent.tsx\");\n/**\n * KaibanJS Super Agent Page\n * \n * This page provides the interface for the KaibanJS super agent workflow\n * with comprehensive content generation capabilities.\n */ \n\nfunction KaibanSuperAgentPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_KaibanSuperAgent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/kaiban-super-agent/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/kaiban-super-agent/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nconst metadata = {\n    title: 'KaibanJS Super Agent - Advanced Multi-Agent Content Generation',\n    description: 'Generate high-quality content using KaibanJS framework with Gemini and Qwen models, powered by Tavily search for comprehensive research and analysis.'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2thaWJhbi1zdXBlci1hZ2VudC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOzs7OztDQUtDO0FBRTREO0FBRTlDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDSCxvRUFBZ0JBOzs7Ozs7Ozs7O0FBR3ZCO0FBRU8sTUFBTUksV0FBVztJQUN0QkMsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAva2FpYmFuLXN1cGVyLWFnZW50L3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogS2FpYmFuSlMgU3VwZXIgQWdlbnQgUGFnZVxuICogXG4gKiBUaGlzIHBhZ2UgcHJvdmlkZXMgdGhlIGludGVyZmFjZSBmb3IgdGhlIEthaWJhbkpTIHN1cGVyIGFnZW50IHdvcmtmbG93XG4gKiB3aXRoIGNvbXByZWhlbnNpdmUgY29udGVudCBnZW5lcmF0aW9uIGNhcGFiaWxpdGllcy5cbiAqL1xuXG5pbXBvcnQgS2FpYmFuU3VwZXJBZ2VudCBmcm9tICdAL2NvbXBvbmVudHMvS2FpYmFuU3VwZXJBZ2VudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEthaWJhblN1cGVyQWdlbnRQYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIDxLYWliYW5TdXBlckFnZW50IC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdLYWliYW5KUyBTdXBlciBBZ2VudCAtIEFkdmFuY2VkIE11bHRpLUFnZW50IENvbnRlbnQgR2VuZXJhdGlvbicsXG4gIGRlc2NyaXB0aW9uOiAnR2VuZXJhdGUgaGlnaC1xdWFsaXR5IGNvbnRlbnQgdXNpbmcgS2FpYmFuSlMgZnJhbWV3b3JrIHdpdGggR2VtaW5pIGFuZCBRd2VuIG1vZGVscywgcG93ZXJlZCBieSBUYXZpbHkgc2VhcmNoIGZvciBjb21wcmVoZW5zaXZlIHJlc2VhcmNoIGFuZCBhbmFseXNpcy4nLFxufTtcbiJdLCJuYW1lcyI6WyJLYWliYW5TdXBlckFnZW50IiwiS2FpYmFuU3VwZXJBZ2VudFBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/kaiban-super-agent/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"]}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"]}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(rsc)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: 'Invincible - Content Writing SaaS',\n    description: 'The Ultimate Content Writing SaaS Platform with AI-powered tools'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_2__.SettingsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/KaibanSuperAgent.tsx":
/*!*********************************************!*\
  !*** ./src/components/KaibanSuperAgent.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSettings: () => (/* binding */ useSettings)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSettings = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx",
"useSettings",
);const SettingsProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx",
"SettingsProvider",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx",
"useTheme",
);const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx",
"ThemeProvider",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(ssr)/./src/contexts/SettingsContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJQb3BwaW5zJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIyd2VpZ2h0JTVDJTIyJTNBJTVCJTVDJTIyMzAwJTVDJTIyJTJDJTVDJTIyNDAwJTVDJTIyJTJDJTVDJTIyNTAwJTVDJTIyJTJDJTVDJTIyNjAwJTVDJTIyJTJDJTVDJTIyNzAwJTVDJTIyJTJDJTVDJTIyODAwJTVDJTIyJTJDJTVDJTIyOTAwJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIycG9wcGlucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFheXVzaG1pc2hyYSUyRkRlc2t0b3AlMkZvbGQlMjBpbnZpbmNpYmxlJTIwd2l0aCUyMGRlZXByZXNlYXJjaCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGY29udGV4dHMlMkZTZXR0aW5nc0NvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2V0dGluZ3NQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFheXVzaG1pc2hyYSUyRkRlc2t0b3AlMkZvbGQlMjBpbnZpbmNpYmxlJTIwd2l0aCUyMGRlZXByZXNlYXJjaCUyRnNyYyUyRmNvbnRleHRzJTJGVGhlbWVDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUF3SztBQUN4SztBQUNBLDBLQUFrSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2V0dGluZ3NQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29udGV4dHMvU2V0dGluZ3NDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29udGV4dHMvVGhlbWVDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FKaibanSuperAgent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FKaibanSuperAgent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/KaibanSuperAgent.tsx */ \"(ssr)/./src/components/KaibanSuperAgent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGY29tcG9uZW50cyUyRkthaWJhblN1cGVyQWdlbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWtLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL0thaWJhblN1cGVyQWdlbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FKaibanSuperAgent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/KaibanSuperAgent.tsx":
/*!*********************************************!*\
  !*** ./src/components/KaibanSuperAgent.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KaibanSuperAgent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Brain,CheckCircle,Clock,Copy,Cpu,Database,Download,Edit3,FileText,Globe,Lightbulb,Rocket,Search,Settings,Sparkles,Square,Star,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction KaibanSuperAgent() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // State management\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        contentType: 'article',\n        targetWordCount: 2000,\n        tone: 'professional',\n        targetAudience: 'intermediate',\n        maxPrimaryResults: 6,\n        maxDeepResults: 4,\n        enableFactChecking: true,\n        includeSourceCitations: true\n    });\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle workflow execution\n    const handleExecute = async ()=>{\n        if (!topic.trim()) {\n            alert('Please enter a topic');\n            return;\n        }\n        setIsRunning(true);\n        setResult(null);\n        setLogs([]);\n        abortControllerRef.current = new AbortController();\n        try {\n            setLogs((prev)=>[\n                    ...prev,\n                    `🚀 Starting KaibanJS Super Agent workflow for: \"${topic}\"`\n                ]);\n            // Simulate progress updates for the 6 phases\n            const phases = [\n                'Topic Analysis',\n                'Content Strategy',\n                'Primary Research',\n                'Gap Analysis',\n                'Deep Research',\n                'Content Generation'\n            ];\n            let currentPhase = 0;\n            const progressInterval = setInterval(()=>{\n                if (currentPhase < phases.length && isRunning) {\n                    const progress = Math.round((currentPhase + 1) / phases.length * 100);\n                    setProgress({\n                        phase: phases[currentPhase],\n                        progress,\n                        message: `Processing ${phases[currentPhase]}...`,\n                        timestamp: new Date().toISOString()\n                    });\n                    setLogs((prev)=>[\n                            ...prev,\n                            `📋 [${phases[currentPhase]}] Processing... (${progress}%)`\n                        ]);\n                    currentPhase++;\n                } else {\n                    clearInterval(progressInterval);\n                }\n            }, 25000); // 25 seconds per phase\n            // Make API call\n            const response = await fetch('/api/kaiban-super-agent', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    topic,\n                    ...options\n                }),\n                signal: abortControllerRef.current.signal\n            });\n            clearInterval(progressInterval);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            if (data.success) {\n                setResult(data.data);\n                setProgress({\n                    phase: 'Completed',\n                    progress: 100,\n                    message: 'Workflow completed successfully!',\n                    timestamp: new Date().toISOString()\n                });\n                setLogs((prev)=>[\n                        ...prev,\n                        '✅ KaibanJS Super Agent workflow completed successfully!'\n                    ]);\n            } else {\n                throw new Error(data.error || 'Workflow failed');\n            }\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                setLogs((prev)=>[\n                        ...prev,\n                        '⏹️ Workflow cancelled by user'\n                    ]);\n                setProgress({\n                    phase: 'Cancelled',\n                    progress: 0,\n                    message: 'Workflow cancelled',\n                    timestamp: new Date().toISOString()\n                });\n            } else {\n                console.error('KaibanJS Super Agent error:', error);\n                setResult({\n                    success: false,\n                    error: error.message || 'An unexpected error occurred'\n                });\n                setLogs((prev)=>[\n                        ...prev,\n                        `❌ Error: ${error.message || 'An unexpected error occurred'}`\n                    ]);\n            }\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    // Handle workflow cancellation\n    const handleCancel = ()=>{\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        setIsRunning(false);\n    };\n    // Copy content to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            alert('Content copied to clipboard!');\n        } catch (err) {\n            console.error('Failed to copy text: ', err);\n        }\n    };\n    // Download content as file\n    const downloadContent = (content, filename)=>{\n        const blob = new Blob([\n            content\n        ], {\n            type: 'text/plain'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    // Open content in enhanced editor\n    const openInEditor = (articleData)=>{\n        // Save to localStorage for the editor to access\n        localStorage.setItem('kaibanGeneratedArticle', JSON.stringify(articleData));\n        // Navigate to editor page\n        router.push('/editor');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse\",\n                        style: {\n                            animationDelay: '4s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center gap-4 mb-6 p-4 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl blur-lg opacity-75\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-4 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-10 w-10 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent\",\n                                                    children: \"KaibanJS Super Agent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1 bg-emerald-500/20 text-emerald-300 rounded-full text-sm font-medium border border-emerald-500/30\",\n                                                            children: \"Multi-Agent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30\",\n                                                            children: \"Enhanced Research\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1 bg-yellow-500/20 text-yellow-300 rounded-full text-sm font-medium border border-yellow-500/30\",\n                                                            children: \"v2.0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        delay: 0.3,\n                                        duration: 0.8\n                                    },\n                                    className: \"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\",\n                                    children: \"Advanced 6-phase multi-agent content generation using KaibanJS framework with Gemini and Qwen models, powered by Tavily search for comprehensive research and competitive analysis. Features enhanced topic analysis with web research, competitive content analysis, and superior content generation that outranks existing articles.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.5,\n                                        duration: 0.8\n                                    },\n                                    className: \"flex flex-wrap justify-center gap-3 mt-6\",\n                                    children: [\n                                        {\n                                            icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                            text: 'Topic Analysis + Web Research',\n                                            color: 'from-emerald-500 to-green-600'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                            text: 'Content Strategy',\n                                            color: 'from-blue-500 to-cyan-600'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                            text: 'Primary Research',\n                                            color: 'from-purple-500 to-pink-600'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                            text: 'Gap Analysis',\n                                            color: 'from-orange-500 to-red-600'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            text: 'Deep Research',\n                                            color: 'from-yellow-500 to-amber-600'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                            text: 'Superior Content Generation + Competitive Analysis',\n                                            color: 'from-red-500 to-pink-600'\n                                        }\n                                    ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"h-4 w-4 text-white/80\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-white/80 font-medium\",\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.2,\n                            duration: 0.8\n                        },\n                        className: \"max-w-5xl mx-auto mb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-6 w-6 text-yellow-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: \"Content Topic\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: topic,\n                                                            onChange: (e)=>setTopic(e.target.value),\n                                                            placeholder: \"Enter your content topic (e.g., 'artificial intelligence in healthcare', 'quantum computing applications')\",\n                                                            className: \"w-full px-6 py-5 text-lg bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 focus:bg-white/15\",\n                                                            disabled: isRunning\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        topic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-emerald-400 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-white\",\n                                                            children: \"Configuration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/80 mb-3\",\n                                                                    children: \"Content Type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: options.contentType,\n                                                                    onChange: (e)=>setOptions((prev)=>({\n                                                                                ...prev,\n                                                                                contentType: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300\",\n                                                                    disabled: isRunning,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"article\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Article\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"blog-post\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Blog Post\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"research-paper\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Research Paper\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"comprehensive-guide\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Comprehensive Guide\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"case-study\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Case Study\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"white-paper\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"White Paper\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"tutorial\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Tutorial\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/80 mb-3\",\n                                                                    children: \"Target Word Count\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: options.targetWordCount,\n                                                                    onChange: (e)=>setOptions((prev)=>({\n                                                                                ...prev,\n                                                                                targetWordCount: parseInt(e.target.value) || 2000\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300\",\n                                                                    disabled: isRunning,\n                                                                    min: \"500\",\n                                                                    max: \"10000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/80 mb-3\",\n                                                                    children: \"Tone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: options.tone,\n                                                                    onChange: (e)=>setOptions((prev)=>({\n                                                                                ...prev,\n                                                                                tone: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300\",\n                                                                    disabled: isRunning,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"professional\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Professional\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"casual\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Casual\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"academic\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Academic\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"conversational\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Conversational\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"authoritative\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Authoritative\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"engaging\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Engaging\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"technical\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Technical\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/80 mb-3\",\n                                                                    children: \"Target Audience\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: options.targetAudience,\n                                                                    onChange: (e)=>setOptions((prev)=>({\n                                                                                ...prev,\n                                                                                targetAudience: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300\",\n                                                                    disabled: isRunning,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"beginner\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Beginner\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"intermediate\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Intermediate\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"expert\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Expert\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"general\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"General\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"technical\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Technical\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"business\",\n                                                                            className: \"bg-gray-800 text-white\",\n                                                                            children: \"Business\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center\",\n                                            children: !isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: handleExecute,\n                                                disabled: !topic.trim(),\n                                                className: \"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl text-lg font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Start Super Agent Workflow\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: handleCancel,\n                                                className: \"flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-2xl hover:from-red-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl text-lg font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Cancel Workflow\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    (isRunning || progress) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"max-w-5xl mx-auto mb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyan-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"6-Phase Multi-Agent Workflow Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this),\n                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-purple-500/20 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 text-purple-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: progress.phase\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-cyan-400\",\n                                                            children: [\n                                                                progress.progress,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-2 bg-white/20 rounded-full overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-full bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full transition-all duration-500\",\n                                                                style: {\n                                                                    width: `${progress.progress}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-white/20 rounded-full h-3 overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                className: \"h-full bg-gradient-to-r from-purple-500 via-cyan-500 to-blue-500 rounded-full\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: `${progress.progress}%`\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    ease: \"easeOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/5 rounded-2xl p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/90 text-lg leading-relaxed\",\n                                                children: progress.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, this),\n                                logs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Execution Log\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-black/30 backdrop-blur-sm rounded-2xl p-4 max-h-48 overflow-y-auto border border-white/10\",\n                                            children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1\n                                                    },\n                                                    className: \"text-sm text-green-300 font-mono mb-1 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-green-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        log\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 11\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"max-w-5xl mx-auto mb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-8 w-8 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-8 w-8 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: result.success ? 'Content Generated Successfully' : 'Workflow Error'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this),\n                                        result.success && result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: ()=>openInEditor(result),\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-lg font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Open in Editor\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: ()=>copyToClipboard(result.content),\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Copy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: ()=>downloadContent(result.content, `${result.title || 'content'}.txt`),\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Download\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 15\n                                }, this),\n                                result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        result.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                            children: [\n                                                {\n                                                    icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                    value: result.wordCount?.toLocaleString() || '0',\n                                                    label: 'Words Generated',\n                                                    color: 'from-blue-500 to-cyan-600',\n                                                    bgColor: 'bg-blue-500/10',\n                                                    borderColor: 'border-blue-500/30'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    value: result.sourcesUsed || 0,\n                                                    label: 'Sources Analyzed',\n                                                    color: 'from-purple-500 to-pink-600',\n                                                    bgColor: 'bg-purple-500/10',\n                                                    borderColor: 'border-purple-500/30'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                                    value: `${result.qualityScore || 0}%`,\n                                                    label: 'Quality Score',\n                                                    color: 'from-emerald-500 to-green-600',\n                                                    bgColor: 'bg-emerald-500/10',\n                                                    borderColor: 'border-emerald-500/30'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                                    value: result.executionTime ? `${Math.round(result.executionTime / 1000)}s` : '0s',\n                                                    label: 'Execution Time',\n                                                    color: 'from-orange-500 to-red-600',\n                                                    bgColor: 'bg-orange-500/10',\n                                                    borderColor: 'border-orange-500/30'\n                                                }\n                                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1\n                                                    },\n                                                    className: `relative p-6 rounded-2xl border ${stat.bgColor} ${stat.borderColor} backdrop-blur-sm`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                                    className: \"h-6 w-6 text-white/80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `text-2xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`,\n                                                                    children: stat.value\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/70 font-medium\",\n                                                            children: stat.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 21\n                                        }, this),\n                                        result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-yellow-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Generated Content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-black/30 rounded-xl p-6 max-h-96 overflow-y-auto border border-white/10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"whitespace-pre-wrap text-sm text-white/90 font-sans leading-relaxed\",\n                                                        children: result.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-500/10 backdrop-blur-sm border border-red-500/30 rounded-2xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Brain_CheckCircle_Clock_Copy_Cpu_Database_Download_Edit3_FileText_Globe_Lightbulb_Rocket_Search_Settings_Sparkles_Square_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-red-300\",\n                                                    children: \"Error occurred during workflow execution\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-200 leading-relaxed\",\n                                            children: result.error\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/KaibanSuperAgent.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/KaibanSuperAgent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useSettings,SettingsProvider,default auto */ \n\nconst defaultSettings = {\n    firstName: 'John',\n    lastName: 'Doe',\n    email: '<EMAIL>',\n    bio: 'Content creator and digital marketer passionate about AI-powered writing.',\n    avatar: '',\n    defaultLanguage: 'en',\n    timezone: 'America/New_York',\n    defaultWordCount: 1000,\n    defaultTone: 'professional',\n    includeResearchByDefault: true,\n    autoSaveEnabled: true,\n    emailNotifications: true,\n    pushNotifications: false,\n    weeklyReports: true,\n    marketingEmails: false,\n    theme: 'dark',\n    accentColor: 'blue',\n    animationsEnabled: true,\n    compactMode: false,\n    profileVisibility: 'private',\n    dataSharing: false,\n    analyticsTracking: true\n};\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useSettings = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\nconst SettingsProvider = ({ children })=>{\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load settings from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            try {\n                const savedSettings = localStorage.getItem('userSettings');\n                if (savedSettings) {\n                    const parsed = JSON.parse(savedSettings);\n                    setSettings({\n                        ...defaultSettings,\n                        ...parsed\n                    });\n                }\n            } catch (err) {\n                console.error('Failed to load settings:', err);\n                setError('Failed to load user settings');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], []);\n    // Save settings to localStorage whenever they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            if (!isLoading) {\n                try {\n                    localStorage.setItem('userSettings', JSON.stringify(settings));\n                } catch (err) {\n                    console.error('Failed to save settings:', err);\n                    setError('Failed to save settings');\n                }\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings,\n        isLoading\n    ]);\n    const updateSettings = (newSettings)=>{\n        setSettings((prev)=>({\n                ...prev,\n                ...newSettings\n            }));\n        setError(null);\n    };\n    const resetSettings = ()=>{\n        setSettings(defaultSettings);\n        setError(null);\n    };\n    const saveSettings = async ()=>{\n        try {\n            setError(null);\n            // Save to backend API\n            const response = await fetch('/api/settings', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to save settings');\n            }\n        // Settings are already saved to localStorage via useEffect\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to save settings';\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        }\n    };\n    const value = {\n        settings,\n        updateSettings,\n        resetSettings,\n        saveSettings,\n        isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/SettingsContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SettingsContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SettingsContext */ \"(ssr)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider,default auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const { settings, updateSettings } = (0,_SettingsContext__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    // Determine actual theme based on settings and system preference\n    const getActualTheme = ()=>{\n        if (settings.theme === 'auto') {\n            if (false) {}\n            return 'dark' // Default fallback\n            ;\n        }\n        return settings.theme;\n    };\n    const actualTheme = getActualTheme();\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (true) return;\n            const root = document.documentElement;\n            // Remove existing theme classes\n            root.classList.remove('dark', 'light');\n            // Add current theme class\n            root.classList.add(actualTheme);\n            // Apply accent color CSS variables\n            const accentColors = {\n                blue: {\n                    primary: '#2563eb',\n                    secondary: '#1e40af',\n                    light: '#60a5fa'\n                },\n                purple: {\n                    primary: '#7c3aed',\n                    secondary: '#6d28d9',\n                    light: '#a78bfa'\n                },\n                green: {\n                    primary: '#059669',\n                    secondary: '#047857',\n                    light: '#34d399'\n                },\n                red: {\n                    primary: '#e11d48',\n                    secondary: '#be185d',\n                    light: '#fb7185'\n                },\n                orange: {\n                    primary: '#d97706',\n                    secondary: '#b45309',\n                    light: '#fbbf24'\n                }\n            };\n            const colors = accentColors[settings.accentColor] || accentColors.blue;\n            root.style.setProperty('--accent-primary', colors.primary);\n            root.style.setProperty('--accent-secondary', colors.secondary);\n            root.style.setProperty('--accent-light', colors.light);\n            // Apply animations setting\n            if (!settings.animationsEnabled) {\n                root.style.setProperty('--animation-duration', '0s');\n                root.style.setProperty('--transition-duration', '0s');\n            } else {\n                root.style.setProperty('--animation-duration', '0.3s');\n                root.style.setProperty('--transition-duration', '0.2s');\n            }\n            // Apply compact mode\n            if (settings.compactMode) {\n                root.classList.add('compact-mode');\n            } else {\n                root.classList.remove('compact-mode');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        actualTheme,\n        settings.accentColor,\n        settings.animationsEnabled,\n        settings.compactMode\n    ]);\n    // Listen for system theme changes when in auto mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (settings.theme !== 'auto' || \"undefined\" === 'undefined') return;\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    // Force re-render by updating a dummy state or triggering theme application\n                    const root = document.documentElement;\n                    root.classList.remove('dark', 'light');\n                    root.classList.add(getActualTheme());\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        settings.theme\n    ]);\n    const setTheme = (theme)=>{\n        updateSettings({\n            theme\n        });\n    };\n    const setAccentColor = (color)=>{\n        updateSettings({\n            accentColor: color\n        });\n    };\n    const toggleAnimations = ()=>{\n        updateSettings({\n            animationsEnabled: !settings.animationsEnabled\n        });\n    };\n    const toggleCompactMode = ()=>{\n        updateSettings({\n            compactMode: !settings.compactMode\n        });\n    };\n    const value = {\n        theme: actualTheme,\n        accentColor: settings.accentColor,\n        animationsEnabled: settings.animationsEnabled,\n        compactMode: settings.compactMode,\n        setTheme,\n        setAccentColor,\n        toggleAnimations,\n        toggleCompactMode\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/motion-dom","vendor-chunks/@swc","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkaiban-super-agent%2Fpage&page=%2Fkaiban-super-agent%2Fpage&appPaths=%2Fkaiban-super-agent%2Fpage&pagePath=private-next-app-dir%2Fkaiban-super-agent%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();