/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kaiban-super-agent/route";
exports.ids = ["app/api/kaiban-super-agent/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_kaiban_super_agent_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kaiban-super-agent/route.ts */ \"(rsc)/./src/app/api/kaiban-super-agent/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kaiban-super-agent/route\",\n        pathname: \"/api/kaiban-super-agent\",\n        filename: \"route\",\n        bundlePath: \"app/api/kaiban-super-agent/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/kaiban-super-agent/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_kaiban_super_agent_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kaiban-super-agent/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/kaiban-super-agent/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_agents_kaiban_super_agent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/agents/kaiban-super-agent */ \"(rsc)/./src/lib/agents/kaiban-super-agent/index.ts\");\n/**\n * KaibanJS Super Agent API Endpoint\n * \n * This API endpoint handles requests for the KaibanJS super agent workflow.\n * It processes content generation requests using the 7-phase workflow with\n * Gemini and Qwen models, integrated with Tavily search.\n */ \n\n// API logging utility\nconst logAPI = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🌐 [KAIBAN-API] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-API-DATA]:`, JSON.stringify(data, null, 2));\n    }\n};\n/**\n * POST handler for KaibanJS Super Agent workflow\n */ async function POST(request) {\n    const startTime = Date.now();\n    try {\n        logAPI('Received KaibanJS Super Agent request');\n        // Parse request body\n        const body = await request.json();\n        // Validate required fields\n        if (!body.topic || typeof body.topic !== 'string' || body.topic.trim().length === 0) {\n            logAPI('Invalid request: missing or empty topic');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Topic is required and must be a non-empty string',\n                timestamp: new Date().toISOString()\n            }, {\n                status: 400\n            });\n        }\n        // Validate environment variables\n        if (false) {}\n        if (false) {}\n        // Tavily is optional - workflow can continue without it\n        if (false) {}\n        // Prepare options\n        const options = {\n            topic: body.topic.trim(),\n            contentType: body.contentType || 'article',\n            targetWordCount: body.targetWordCount || 2000,\n            tone: body.tone || 'professional',\n            targetAudience: body.targetAudience || 'intermediate',\n            maxPrimaryResults: body.maxPrimaryResults || 6,\n            maxDeepResults: body.maxDeepResults || 4,\n            enableFactChecking: body.enableFactChecking ?? true,\n            includeSourceCitations: body.includeSourceCitations ?? true\n        };\n        logAPI('Starting KaibanJS Super Agent workflow', {\n            topic: options.topic,\n            contentType: options.contentType,\n            targetWordCount: options.targetWordCount,\n            targetAudience: options.targetAudience\n        });\n        // Create progress callback for logging\n        const onProgress = (phase, progress, message)=>{\n            logAPI(`Progress: ${phase} (${progress}%)`, {\n                message\n            });\n        };\n        // Initialize and execute the super agent\n        const superAgent = new _lib_agents_kaiban_super_agent__WEBPACK_IMPORTED_MODULE_1__.KaibanSuperAgent(onProgress);\n        const result = await superAgent.execute(options);\n        const totalTime = Date.now() - startTime;\n        if (result.success) {\n            logAPI('KaibanJS Super Agent workflow completed successfully', {\n                executionTime: `${totalTime}ms`,\n                wordCount: result.wordCount,\n                sourcesUsed: result.sourcesUsed,\n                qualityScore: result.qualityScore\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: result,\n                timestamp: new Date().toISOString()\n            });\n        } else {\n            logAPI('KaibanJS Super Agent workflow failed', {\n                error: result.error,\n                executionTime: `${totalTime}ms`\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: result.error || 'Workflow execution failed',\n                timestamp: new Date().toISOString()\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        const totalTime = Date.now() - startTime;\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        logAPI('KaibanJS Super Agent API error', {\n            error: errorMessage,\n            executionTime: `${totalTime}ms`,\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * GET handler for API status and configuration\n */ async function GET() {\n    try {\n        logAPI('Health check request received');\n        const status = {\n            service: 'KaibanJS Super Agent API',\n            status: 'operational',\n            version: '1.0.0',\n            features: [\n                '6-phase super agent workflow',\n                'Gemini and Qwen model integration',\n                'Tavily search integration',\n                'Enhanced topic analysis with web research',\n                'Competitive content analysis and pattern recognition',\n                'Superior content generation that outranks existing articles'\n            ],\n            configuration: {\n                geminiConfigured: !!\"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\",\n                openrouterConfigured: !!\"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\",\n                tavilyConfigured: !!\"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\",\n                totalAgents: 6,\n                totalTasks: 6,\n                supportedContentTypes: [\n                    'article',\n                    'blog-post',\n                    'research-paper',\n                    'comprehensive-guide',\n                    'case-study',\n                    'white-paper',\n                    'tutorial'\n                ],\n                supportedTones: [\n                    'professional',\n                    'casual',\n                    'academic',\n                    'conversational',\n                    'authoritative',\n                    'engaging',\n                    'technical'\n                ],\n                supportedAudiences: [\n                    'beginner',\n                    'intermediate',\n                    'expert',\n                    'general',\n                    'technical',\n                    'business'\n                ]\n            },\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(status);\n    } catch (error) {\n        logAPI('Health check error', {\n            error\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            service: 'KaibanJS Super Agent API',\n            status: 'error',\n            error: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * OPTIONS handler for CORS\n */ async function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kaiban-super-agent/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts":
/*!*****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/agents.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentConfig: () => (/* binding */ agentConfig),\n/* harmony export */   contentGenerationAgent: () => (/* binding */ contentGenerationAgent),\n/* harmony export */   contentStrategyAgent: () => (/* binding */ contentStrategyAgent),\n/* harmony export */   deepResearchAgent: () => (/* binding */ deepResearchAgent),\n/* harmony export */   gapAnalysisAgent: () => (/* binding */ gapAnalysisAgent),\n/* harmony export */   primaryResearchAgent: () => (/* binding */ primaryResearchAgent),\n/* harmony export */   superAgentTeam: () => (/* binding */ superAgentTeam),\n/* harmony export */   topicAnalysisAgent: () => (/* binding */ topicAnalysisAgent)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tools */ \"(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts\");\n/**\n * KaibanJS Agents Configuration for Super Agent Workflow\n * \n * This file defines all the agents that will execute the super agent workflow\n * using KaibanJS framework with Gemini and Qwen models.\n * \n * Following user preferences:\n * - Use Qwen model for high reasoning phases (1, 4, 7)\n * - Use Gemini model for other phases\n * - Double the max output and input tokens\n * - Use Tavily search for web research\n */ \n\n// Console logging utility for Kaiban Agents\nconst logKaibanAgent = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🤖 [KAIBAN-AGENT] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-AGENT-DATA]:`, data);\n    }\n};\n/**\n * Model Configuration\n * Following user preferences for doubled tokens and specific model usage\n */ const QWEN_MODEL_CONFIG = {\n    provider: 'openai',\n    model: 'qwen/qwen3-235b-a22b-04-28',\n    apiKey: \"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\",\n    apiBaseUrl: 'https://openrouter.ai/api/v1',\n    maxTokens: 8000,\n    temperature: 0.3\n};\nconst GEMINI_MODEL_CONFIG = {\n    provider: 'google',\n    model: 'gemini-2.0-flash',\n    apiKey: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\",\n    maxTokens: 8000,\n    temperature: 0.7\n};\nlogKaibanAgent('Initializing KaibanJS Super Agent Team');\n/**\n * Phase 1: Topic Analysis Agent (High Reasoning - Qwen + Tavily)\n * Analyzes the input topic and creates comprehensive research strategy with web research\n */ const topicAnalysisAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'TopicAnalyst',\n    role: 'Senior Research Strategist & Topic Intelligence Analyst',\n    goal: 'Conduct comprehensive topic analysis using web research to create detailed, data-driven research strategies for content creation',\n    background: 'Expert research strategist with deep knowledge across multiple domains, specializing in topic analysis, trend identification, and strategic research planning. Skilled in using web research to understand current landscape, identify key players, and discover emerging trends.',\n    llmConfig: QWEN_MODEL_CONFIG,\n    tools: _tools__WEBPACK_IMPORTED_MODULE_1__.kaibanTools\n});\n/**\n * Phase 2: Content Strategy Agent (Gemini)\n * Develops content strategy based on topic analysis\n */ const contentStrategyAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'ContentStrategist',\n    role: 'Professional Content Strategist',\n    goal: 'Develop comprehensive content strategies that align with audience needs and business objectives',\n    background: 'Seasoned content strategist with expertise in audience analysis, content planning, and engagement optimization.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Phase 3: Primary Research Agent (Gemini + Tavily)\n * Conducts initial web research using Tavily search\n */ const primaryResearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'PrimaryResearcher',\n    role: 'Senior Research Analyst',\n    goal: 'Conduct comprehensive primary research using advanced search techniques and reliable sources',\n    background: 'Expert research analyst specializing in information gathering and source evaluation using advanced search strategies.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: _tools__WEBPACK_IMPORTED_MODULE_1__.kaibanTools\n});\n/**\n * Phase 4: Gap Analysis Agent (High Reasoning - Qwen)\n * Analyzes research gaps and determines additional research needs\n */ const gapAnalysisAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'GapAnalyst',\n    role: 'Research Quality Analyst',\n    goal: 'Identify content gaps and determine additional research requirements for comprehensive coverage',\n    background: 'Meticulous research quality analyst with expertise in identifying information gaps and ensuring comprehensive topic coverage.',\n    llmConfig: QWEN_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Phase 5: Deep Research Agent (Gemini + Tavily)\n * Conducts targeted deep research based on gap analysis\n */ const deepResearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'DeepResearcher',\n    role: 'Specialized Research Expert',\n    goal: 'Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness',\n    background: 'Specialized research expert who excels at finding specific, detailed information to fill knowledge gaps.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: _tools__WEBPACK_IMPORTED_MODULE_1__.kaibanTools\n});\n/**\n * Phase 6: Content Generation Agent (Gemini + Tavily)\n * Creates superior content by analyzing competitors and mimicking successful human writing patterns\n */ const contentGenerationAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'ContentWriter',\n    role: 'Elite Content Creator & Competitive Analysis Expert',\n    goal: 'Create the most informational, well-written, and engaging content that outranks any existing article by analyzing and surpassing human writing patterns',\n    background: 'Elite content creator with exceptional writing skills, deep understanding of human psychology, competitive analysis expertise, and proven ability to create content that outperforms top-ranking articles. Specializes in analyzing successful writing patterns and creating superior content.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: _tools__WEBPACK_IMPORTED_MODULE_1__.kaibanTools\n});\n/**\n * Export all agents as a team\n */ const superAgentTeam = [\n    topicAnalysisAgent,\n    contentStrategyAgent,\n    primaryResearchAgent,\n    gapAnalysisAgent,\n    deepResearchAgent,\n    contentGenerationAgent\n];\nlogKaibanAgent('Super Agent Team created successfully', {\n    totalAgents: superAgentTeam.length,\n    qwenAgents: [\n        'TopicAnalyst',\n        'GapAnalyst'\n    ],\n    geminiAgents: [\n        'ContentStrategist',\n        'PrimaryResearcher',\n        'DeepResearcher',\n        'ContentWriter'\n    ],\n    agentsWithTools: [\n        'TopicAnalyst',\n        'PrimaryResearcher',\n        'DeepResearcher',\n        'ContentWriter'\n    ],\n    maxTokens: 8000,\n    status: 'ready'\n});\n/**\n * Agent configuration for easy reference\n */ const agentConfig = {\n    agents: superAgentTeam,\n    agentNames: [\n        'TopicAnalyst',\n        'ContentStrategist',\n        'PrimaryResearcher',\n        'GapAnalyst',\n        'DeepResearcher',\n        'ContentWriter'\n    ],\n    totalAgents: superAgentTeam.length,\n    modelDistribution: {\n        qwen: 2,\n        gemini: 4 // Other phases\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/index.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/index.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KaibanSuperAgent: () => (/* binding */ KaibanSuperAgent),\n/* harmony export */   agentConfig: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig),\n/* harmony export */   kaibanTools: () => (/* reexport safe */ _tools__WEBPACK_IMPORTED_MODULE_3__.kaibanTools),\n/* harmony export */   superAgentTasks: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks),\n/* harmony export */   superAgentTeam: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam),\n/* harmony export */   taskConfig: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts\");\n/* harmony import */ var _tasks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tasks */ \"(rsc)/./src/lib/agents/kaiban-super-agent/tasks.ts\");\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tools */ \"(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts\");\n/**\n * KaibanJS Super Agent System\n * \n * Main orchestration system for the KaibanJS-based super agent workflow.\n * Integrates Gemini and Qwen models with Tavily search following the\n * super agent workflow pattern.\n * \n * Features:\n * - 6-phase super agent workflow\n * - Qwen model for high reasoning phases (1, 4)\n * - Gemini model for other phases\n * - Tavily search integration with enhanced topic analysis\n * - Competitive content analysis and pattern recognition\n * - Superior content generation that outranks existing articles\n * - Comprehensive error handling and logging\n */ \n\n\n\n// Console logging utility for Kaiban System\nconst logKaibanSystem = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🚀 [KAIBAN-SYSTEM] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-SYSTEM-DATA]:`, data);\n    }\n};\n/**\n * KaibanJS Super Agent Class\n */ class KaibanSuperAgent {\n    constructor(onProgress){\n        this.isInitialized = false;\n        this.onProgress = onProgress;\n        logKaibanSystem('Initializing KaibanJS Super Agent System');\n        // Validate tools before initialization (non-blocking for Tavily)\n        const tavilyValid = (0,_tools__WEBPACK_IMPORTED_MODULE_3__.validateTavilyTool)();\n        if (!tavilyValid) {\n            logKaibanSystem('Warning: Tavily tool validation failed - search functionality will be limited');\n        }\n        // Create the KaibanJS team\n        this.team = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Team({\n            name: 'Super Agent Team',\n            agents: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam,\n            tasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks,\n            inputs: {\n                topic: '',\n                contentType: 'article',\n                targetAudience: 'intermediate',\n                wordCount: 2000,\n                tone: 'professional'\n            },\n            env: {\n                OPENROUTER_API_KEY: \"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\",\n                GEMINI_API_KEY: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\",\n                TAVILY_API_KEY: \"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\"\n            }\n        });\n        this.isInitialized = true;\n        logKaibanSystem('KaibanJS Super Agent System initialized successfully', {\n            totalAgents: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.totalAgents,\n            totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n            toolsConfigured: [\n                'KaibanJS TavilySearchResults'\n            ],\n            modelsConfigured: [\n                'qwen/qwen3-235b-a22b-04-28',\n                'gemini-2.0-flash'\n            ]\n        });\n    }\n    /**\n   * Execute the super agent workflow\n   */ async execute(options) {\n        if (!this.isInitialized) {\n            throw new Error('KaibanJS Super Agent System not initialized');\n        }\n        const startTime = Date.now();\n        logKaibanSystem('Starting super agent workflow execution', {\n            topic: options.topic,\n            contentType: options.contentType,\n            targetWordCount: options.targetWordCount,\n            targetAudience: options.targetAudience\n        });\n        try {\n            // Update progress\n            this.updateProgress('initialization', 0, 'Initializing workflow...');\n            // Execute the workflow - start() method returns the WorkflowResult directly\n            this.updateProgress('execution', 10, 'Starting task execution...');\n            const result = await this.team.start({\n                topic: options.topic,\n                contentType: options.contentType || 'article',\n                targetAudience: options.targetAudience || 'intermediate',\n                wordCount: options.targetWordCount || 2000,\n                tone: options.tone || 'professional'\n            });\n            const executionTime = Date.now() - startTime;\n            logKaibanSystem('Super agent workflow completed successfully', {\n                executionTime: `${executionTime}ms`,\n                tasksCompleted: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                workflowResult: result\n            });\n            // Process and return results\n            return this.processResults(result, executionTime);\n        } catch (error) {\n            const executionTime = Date.now() - startTime;\n            logKaibanSystem('Super agent workflow failed', {\n                error: error instanceof Error ? error.message : 'Unknown error',\n                executionTime: `${executionTime}ms`\n            });\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error occurred',\n                executionTime,\n                metadata: {\n                    totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    completedTasks: 0,\n                    failedTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    agentsUsed: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames,\n                    modelsUsed: [\n                        'meta-llama/llama-3.1-8b-instruct:free',\n                        'gemini-2.0-flash'\n                    ],\n                    searchQueriesExecuted: 0\n                }\n            };\n        }\n    }\n    /**\n   * Process workflow results\n   */ processResults(result, executionTime) {\n        try {\n            // KaibanJS WorkflowResult structure: { result: string, stats: WorkflowStats }\n            const finalOutput = result.result || result.output || 'No content generated';\n            const stats = result.stats || {};\n            logKaibanSystem('Processing workflow results', {\n                hasResult: !!result.result,\n                hasStats: !!result.stats,\n                resultType: typeof result.result,\n                statsKeys: Object.keys(stats)\n            });\n            return {\n                success: true,\n                content: finalOutput,\n                title: this.extractTitle(finalOutput),\n                wordCount: this.countWords(finalOutput),\n                sourcesUsed: this.countSources(result),\n                qualityScore: this.calculateQualityScore(result),\n                executionTime,\n                phaseResults: this.extractPhaseResults(result),\n                metadata: {\n                    totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    completedTasks: stats.completedTasks || _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    failedTasks: stats.failedTasks || 0,\n                    agentsUsed: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames,\n                    modelsUsed: [\n                        'meta-llama/llama-3.1-8b-instruct:free',\n                        'gemini-2.0-flash'\n                    ],\n                    searchQueriesExecuted: this.countSearchQueries(result)\n                }\n            };\n        } catch (error) {\n            logKaibanSystem('Error processing results', {\n                error\n            });\n            return {\n                success: false,\n                error: 'Failed to process workflow results',\n                executionTime,\n                metadata: {\n                    totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    completedTasks: 0,\n                    failedTasks: 1,\n                    agentsUsed: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames,\n                    modelsUsed: [\n                        'meta-llama/llama-3.1-8b-instruct:free',\n                        'gemini-2.0-flash'\n                    ],\n                    searchQueriesExecuted: 0\n                }\n            };\n        }\n    }\n    /**\n   * Update progress callback\n   */ updateProgress(phase, progress, message) {\n        if (this.onProgress) {\n            this.onProgress(phase, progress, message);\n        }\n    }\n    /**\n   * Helper methods for result processing\n   */ extractTitle(content) {\n        const lines = content.split('\\n');\n        const titleLine = lines.find((line)=>line.trim().startsWith('#') || line.trim().length > 10);\n        return titleLine?.replace(/^#+\\s*/, '').trim() || 'Generated Content';\n    }\n    countWords(content) {\n        return content.split(/\\s+/).filter((word)=>word.length > 0).length;\n    }\n    countSources(result) {\n        // Count unique sources from the result content\n        const content = result.result || result.output || '';\n        if (typeof content === 'string') {\n            const urls = content.match(/https?:\\/\\/[^\\s]+/g) || [];\n            return new Set(urls).size;\n        }\n        return 0;\n    }\n    calculateQualityScore(result) {\n        // Simple quality scoring based on result availability and stats\n        const stats = result.stats || {};\n        if (stats.completedTasks && stats.totalTasks) {\n            return Math.round(stats.completedTasks / stats.totalTasks * 100);\n        }\n        // If we have a result, assume good quality\n        return result.result ? 85 : 0;\n    }\n    extractPhaseResults(result) {\n        const phaseResults = {};\n        const stats = result.stats || {};\n        // Create phase results based on available information\n        _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.taskNames.forEach((phaseName, index)=>{\n            phaseResults[phaseName] = {\n                status: 'completed',\n                output: index === _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.taskNames.length - 1 ? result.result : 'Phase completed',\n                agent: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames[index],\n                executionTime: 0 // KaibanJS doesn't provide individual task times\n            };\n        });\n        return phaseResults;\n    }\n    countSearchQueries(result) {\n        // Estimate search queries based on content\n        const content = result.result || result.output || '';\n        if (typeof content === 'string') {\n            // Look for search-related indicators\n            const searchMatches = content.match(/search|research|found|according to|source/gi) || [];\n            return Math.min(searchMatches.length, 20); // Cap at reasonable number\n        }\n        return 0;\n    }\n}\n/**\n * Export configuration and utilities\n */ \n\n\n\nlogKaibanSystem('KaibanJS Super Agent System module loaded successfully');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/tasks.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/tasks.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contentGenerationTask: () => (/* binding */ contentGenerationTask),\n/* harmony export */   contentStrategyTask: () => (/* binding */ contentStrategyTask),\n/* harmony export */   deepResearchTask: () => (/* binding */ deepResearchTask),\n/* harmony export */   gapAnalysisTask: () => (/* binding */ gapAnalysisTask),\n/* harmony export */   primaryResearchTask: () => (/* binding */ primaryResearchTask),\n/* harmony export */   superAgentTasks: () => (/* binding */ superAgentTasks),\n/* harmony export */   taskConfig: () => (/* binding */ taskConfig),\n/* harmony export */   topicAnalysisTask: () => (/* binding */ topicAnalysisTask)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts\");\n/**\n * KaibanJS Tasks for Super Agent Workflow\n *\n * This file defines all the tasks that agents will execute in sequence\n * to complete the super agent workflow using KaibanJS framework.\n * \n * Following the 7-phase super agent workflow pattern with enhanced\n * task definitions and proper agent assignments.\n */ \n\n// Console logging utility for Kaiban Tasks\nconst logKaibanTask = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`📋 [KAIBAN-TASK] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-TASK-DATA]:`, data);\n    }\n};\nlogKaibanTask('Initializing KaibanJS Super Agent Tasks');\n/**\n * Phase 1: Topic Analysis Task (Qwen - High Reasoning + Tavily Web Research)\n * Comprehensive topic analysis with web research and strategic planning\n */ const topicAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `<thinking>\n    I need to conduct comprehensive topic analysis using both my reasoning capabilities and web research.\n    Let me start by understanding the topic landscape through web research, then apply strategic analysis.\n\n    My approach:\n    1. First, conduct broad web searches to understand the current landscape\n    2. Analyze search results to identify key themes and trends\n    3. Perform targeted searches for specific aspects\n    4. Synthesize findings into a comprehensive research strategy\n\n    I should start wide with general searches, then narrow down to specific aspects.\n    </thinking>\n\n    **Your Role:** You are a Senior Research Strategist & Topic Intelligence Analyst with expertise in web research, trend analysis, and strategic research planning.\n\n    **CRITICAL INSTRUCTIONS:**\n    - Use the Tavily search tool extensively to understand the current topic landscape\n    - If Tavily search encounters connection issues, proceed with analysis based on your knowledge\n    - Start with broad searches, then progressively narrow focus\n    - Analyze search results to identify patterns, trends, and gaps\n    - Think like an expert human researcher conducting preliminary investigation\n    - Handle search failures gracefully and continue with comprehensive analysis\n\n    **Phase 1: Web Research & Discovery**\n    1. **Initial Landscape Search**: Search for the main topic to understand current state\n    2. **Trend Analysis**: Search for \"topic + trends 2025\" and \"topic + latest developments\"\n    3. **Expert Perspectives**: Search for \"topic + expert opinions\" and \"topic + industry leaders\"\n    4. **Competitive Analysis**: Search for \"best content about topic\" and \"topic + comprehensive guide\"\n    5. **Problem Identification**: Search for \"topic + challenges\" and \"topic + problems\"\n    6. **Solution Mapping**: Search for \"topic + solutions\" and \"topic + best practices\"\n\n    **Phase 2: Strategic Analysis (After Web Research)**\n    1. **Topic Decomposition**: Break down into 4-6 key subtopics based on research findings\n    2. **Keyword Intelligence**: Extract high-value terms from search results\n    3. **Content Gap Analysis**: Identify what's missing in existing content\n    4. **Audience Intent Mapping**: Determine what users are actually searching for\n    5. **Authority Source Identification**: Find the most credible sources in the space\n    6. **Competitive Landscape**: Map key players and their content strategies\n    7. **Trend Integration**: Identify 2025 trends to incorporate\n    8. **Search Query Optimization**: Create targeted queries for subsequent research phases\n\n    **Research Strategy Framework:**\n    - Execute 6-8 strategic web searches using Tavily with correct parameter \"searchQuery\"\n    - Use format: {\"searchQuery\": \"your search terms here\"}\n    - Analyze each search result for insights and patterns\n    - Document key findings and authoritative sources\n    - Create a comprehensive research roadmap\n\n    **Topic to Analyze:** {topic}\n    **Content Type:** {contentType}\n    **Target Audience:** {targetAudience}\n    **Word Count Target:** {wordCount}\n\n    **EXECUTION APPROACH:**\n    1. Attempt broad topic searches to understand the landscape\n    2. If search tools are unavailable, use your extensive knowledge base\n    3. Progressively narrow searches based on initial findings\n    4. Focus on recent content (2024-2025) for current trends\n    5. Prioritize authoritative sources and expert perspectives\n    6. Document all findings with source attribution\n    7. Synthesize research into actionable strategy\n\n    **FALLBACK STRATEGY (if search tools fail):**\n    - Leverage your comprehensive knowledge base for topic analysis\n    - Focus on established patterns and known industry trends\n    - Provide strategic analysis based on fundamental principles\n    - Include recommendations for manual research verification\n    - Maintain high-quality analysis standards regardless of tool availability`,\n    expectedOutput: `Comprehensive web-research-based topic analysis including:\n    - Current topic landscape analysis with key findings from web research\n    - Authoritative sources and expert perspectives discovered\n    - Trending developments and 2025 insights\n    - Strategic subtopic breakdown based on research findings\n    - High-value keywords and semantic terms from actual search results\n    - Content gaps identified through competitive analysis\n    - Targeted search queries for subsequent research phases\n    - Research strategy roadmap with priority areas\n    - Source credibility assessment and authority mapping\n    - Audience intent analysis based on search patterns`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[0]\n});\n/**\n * Phase 2: Content Strategy Task (Gemini)\n * Develop comprehensive content strategy based on topic analysis\n */ const contentStrategyTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Develop a comprehensive content strategy based on the topic analysis.\n    \n    **Your Role:** You are a Professional Content Strategist specializing in audience-focused content planning.\n    \n    **Task Requirements:**\n    1. **Content Goal Definition**: Define clear content objectives\n    2. **Audience Targeting**: Refine target audience based on analysis\n    3. **Content Structure**: Design optimal content structure and flow\n    4. **Key Messages**: Identify 3-5 core messages to communicate\n    5. **Hook Development**: Create compelling opening hooks\n    6. **Call-to-Action Strategy**: Design effective CTAs\n    7. **SEO Strategy**: Develop keyword strategy and search intent alignment\n    8. **Narrative Structure**: Plan story arc and content flow\n    9. **Engagement Elements**: Identify opportunities for audience engagement\n    10. **Content Differentiation**: Define unique value proposition\n    \n    **Input:** Use the topic analysis results from the previous task.\n    \n    **Output Format:**\n    Provide a detailed content strategy document with actionable recommendations.`,\n    expectedOutput: `Detailed content strategy including:\n    - Content goals and audience targeting\n    - Recommended content structure and flow\n    - Key messages and narrative structure\n    - Hook strategies and call-to-action plans\n    - SEO strategy with keyword recommendations\n    - Engagement and differentiation strategies`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[1]\n});\n/**\n * Phase 3: Primary Research Task (Gemini + Tavily)\n * Conduct comprehensive primary research using advanced search strategies\n */ const primaryResearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `<thinking>\n    I need to conduct systematic primary research using the strategic queries from topic analysis.\n    I should follow expert research methodologies: start broad, evaluate sources, extract insights,\n    and organize findings systematically. I'll use parallel search strategies to cover multiple\n    aspects simultaneously while maintaining source quality standards.\n    </thinking>\n\n    **Your Role:** You are a Senior Research Analyst specializing in systematic information gathering, source evaluation, and evidence synthesis.\n\n    **RESEARCH METHODOLOGY:**\n    Follow professional research standards used by expert analysts:\n    1. Execute strategic queries from topic analysis systematically\n    2. Evaluate each source for authority, recency, and relevance\n    3. Extract key insights and supporting evidence\n    4. Cross-reference information across multiple sources\n    5. Organize findings by subtopic and importance\n    6. Document source quality and credibility\n\n    **ADVANCED SEARCH STRATEGY:**\n    1. **Strategic Query Execution**: Use the optimized search queries from topic analysis\n    2. **Source Diversification**: Target different types of sources (academic, industry, news, expert)\n    3. **Authority Prioritization**: Focus on recognized experts and authoritative publications\n    4. **Recency Filtering**: Prioritize 2024-2025 content for current insights\n    5. **Cross-Validation**: Verify key claims across multiple sources\n    6. **Evidence Hierarchy**: Rank sources by credibility and relevance\n\n    **INFORMATION EXTRACTION FRAMEWORK:**\n    1. **Key Insights**: Extract main findings and conclusions\n    2. **Supporting Evidence**: Gather statistics, data points, and examples\n    3. **Expert Perspectives**: Capture authoritative opinions and analysis\n    4. **Trend Identification**: Document emerging patterns and developments\n    5. **Practical Applications**: Find real-world implementations and case studies\n    6. **Challenges & Solutions**: Identify problems and proposed solutions\n\n    **QUALITY STANDARDS:**\n    - Prioritize primary sources over secondary sources\n    - Verify statistical claims and data points\n    - Assess source bias and perspective\n    - Document publication dates and author credentials\n    - Cross-reference controversial or disputed claims\n    - Filter out SEO-optimized content farms\n\n    **PARALLEL RESEARCH APPROACH:**\n    Execute 3-5 searches simultaneously covering:\n    - Core topic fundamentals and definitions\n    - Current trends and recent developments\n    - Expert opinions and industry analysis\n    - Statistical data and research findings\n    - Practical applications and case studies\n\n    **Tool Usage Instructions:**\n    Use Tavily search tool with strategic queries. IMPORTANT: Use the correct parameter name \"searchQuery\" (not \"query\"). For each search:\n    1. Call the tool with: {\"searchQuery\": \"your search terms here\"}\n    2. Analyze all returned results for quality and relevance\n    3. Extract key information from top-quality sources\n    4. Document source details for citation purposes\n    5. Note any information gaps for deep research phase\n\n    **If search tools encounter issues:**\n    - Continue with comprehensive analysis using your knowledge base\n    - Focus on established research methodologies and known sources\n    - Provide detailed analysis based on fundamental principles\n    - Include recommendations for manual verification\n\n    **Input:** Use strategic search queries and research priorities from topic analysis and content strategy.`,\n    expectedOutput: `Systematic research findings organized as:\n    - Executive summary of key findings and insights\n    - Subtopic-organized information with source attribution\n    - Authority source analysis with credibility ratings\n    - Current trends and 2025 developments with evidence\n    - Statistical data and supporting evidence compilation\n    - Expert perspectives and authoritative opinions\n    - Practical applications and real-world examples\n    - Information gaps identified for targeted deep research\n    - Source quality assessment and reliability ratings\n    - Cross-referenced claims and validated information`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[2]\n});\nlogKaibanTask('Primary research task created with Tavily integration');\n/**\n * Phase 4: Gap Analysis Task (Qwen - High Reasoning)\n * Analyze research gaps and determine additional research needs\n */ const gapAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `<thinking>\n    I need to systematically analyze the primary research results to identify gaps and weaknesses.\n    I should approach this like a quality assurance expert, comparing what we have against what we need\n    for comprehensive content. I'll evaluate coverage, source diversity, depth, and identify specific\n    areas that need additional research.\n    </thinking>\n\n    **Your Role:** You are a Research Quality Analyst and Information Architecture Expert with expertise in identifying information gaps and ensuring comprehensive coverage.\n\n    **SYSTEMATIC GAP ANALYSIS METHODOLOGY:**\n    Follow professional research quality assessment standards:\n    1. Map research findings against content strategy requirements\n    2. Identify coverage gaps in each subtopic area\n    3. Assess source diversity and authority levels\n    4. Evaluate information depth and detail sufficiency\n    5. Check for bias patterns and perspective limitations\n    6. Analyze currency and relevance of information\n\n    **COMPREHENSIVE EVALUATION FRAMEWORK:**\n    1. **Coverage Assessment**:\n       - Compare research against all subtopics from topic analysis\n       - Identify areas with insufficient information\n       - Map content strategy requirements against available data\n\n    2. **Source Quality Analysis**:\n       - Evaluate source authority and credibility distribution\n       - Assess need for more authoritative sources\n       - Identify missing expert perspectives\n\n    3. **Information Depth Evaluation**:\n       - Determine areas needing deeper investigation\n       - Identify superficial coverage requiring enhancement\n       - Assess practical application and implementation details\n\n    4. **Bias and Perspective Analysis**:\n       - Identify potential biases in current research\n       - Assess perspective diversity and balance\n       - Determine need for alternative viewpoints\n\n    5. **Currency and Relevance Check**:\n       - Evaluate information recency and current relevance\n       - Identify areas needing 2025 updates and trends\n       - Assess need for recent developments and changes\n\n    **PRIORITY RANKING SYSTEM:**\n    Rank identified gaps by:\n    - Critical importance to content objectives\n    - Impact on content quality and authority\n    - Audience value and interest level\n    - Availability of reliable sources\n    - Time and effort required for research\n\n    **TARGETED RESEARCH RECOMMENDATIONS:**\n    For each identified gap, provide:\n    - Specific search queries to fill the gap\n    - Recommended source types and authorities\n    - Expected information depth and detail level\n    - Priority level and research effort estimation\n\n    **Input:** Analyze primary research results against topic analysis and content strategy requirements.`,\n    expectedOutput: `Systematic gap analysis report including:\n    - Executive summary of research quality and completeness\n    - Detailed gap identification with priority rankings\n    - Source diversity and authority assessment\n    - Information depth evaluation by subtopic\n    - Bias and perspective analysis with recommendations\n    - Currency and relevance assessment\n    - Specific targeted research recommendations with search queries\n    - Priority-ranked action items for deep research phase\n    - Quality improvement roadmap and success metrics`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[3]\n});\nlogKaibanTask('Gap analysis task created for research quality assessment');\n/**\n * Phase 5: Deep Research Task (Gemini + Tavily)\n * Conduct targeted deep research based on gap analysis\n */ const deepResearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness.\n    \n    **Your Role:** You are a Specialized Research Expert who excels at finding specific, detailed information to fill knowledge gaps.\n    \n    **Task Requirements:**\n    1. **Targeted Searches**: Execute specific queries to fill identified gaps\n    2. **Expert Source Finding**: Locate authoritative expert perspectives\n    3. **Detailed Investigation**: Dive deep into complex aspects\n    4. **Specialized Information**: Find technical or specialized details\n    5. **Case Studies**: Locate relevant examples and case studies\n    6. **Recent Developments**: Find latest news and developments\n    7. **Statistical Enhancement**: Gather additional data and statistics\n    8. **Comparative Analysis**: Find comparative information and benchmarks\n    9. **Implementation Details**: Research practical applications and methods\n    10. **Validation**: Cross-reference and validate critical information\n    \n    **Search Strategy:**\n    - Focus on gap analysis recommendations\n    - Use specialized and technical search terms\n    - Target expert and authoritative sources\n    - Search for recent developments and updates\n    - Look for case studies and practical examples\n    \n    **Tool Usage:**\n    Use the Tavily search tool with targeted queries based on gap analysis. IMPORTANT: Use the correct parameter name \"searchQuery\" (not \"query\").\n    Example: {\"searchQuery\": \"your targeted search terms here\"}\n\n    **Input:** Use gap analysis recommendations and specific research needs identified.`,\n    expectedOutput: `Targeted deep research results including:\n    - Specific information filling identified gaps\n    - Expert perspectives and authoritative sources\n    - Detailed technical or specialized information\n    - Case studies and practical examples\n    - Recent developments and updates\n    - Enhanced statistical data and evidence`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[4]\n});\nlogKaibanTask('Deep research task created for targeted investigation');\n/**\n * Phase 6: Content Generation Task (Gemini + Tavily)\n * Generate superior content using keyword extraction and top 5 page analysis\n */ const contentGenerationTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `<thinking>\n    I need to create superior content using a strategic approach:\n    1. First, extract the single most important keyword from the topic using my analytical capabilities\n    2. Use that keyword to search for the top 5 ranking pages\n    3. Extract and analyze content from those pages\n    4. Use the comprehensive research data to create superior content\n    5. Ensure the content outranks existing articles through better information and structure\n\n    This keyword-focused approach will help me find the most relevant competitive content and create something that surpasses it.\n    </thinking>\n\n    **Your Role:** You are an Elite Content Creator and SEO Expert with exceptional analytical skills, keyword intelligence, and expertise in creating content that outranks competitors through strategic research and superior writing.\n\n    **CRITICAL MISSION:** Create the most informational, well-written, and engaging article that will outrank any existing content on this topic using strategic keyword extraction and competitive analysis.\n\n    **PHASE 1: KEYWORD EXTRACTION & STRATEGIC SEARCH**\n    1. **Single Keyword Extraction**:\n       - Analyze the topic: \"{topic}\"\n       - Extract the single most important, searchable keyword that represents the core of this topic\n       - This should be the primary keyword that people would search for when looking for this information\n       - Focus on the main concept, not modifiers or secondary terms\n       - Example: For \"Best AI Tools for Content Creation in 2025\" → extract \"AI tools\"\n\n    2. **Top 5 Page Research**:\n       - Use the extracted keyword to search and find the top 5 ranking pages\n       - Search query should be exactly the extracted keyword (simple, direct search)\n       - Focus on pages that rank highest for this keyword\n       - Prioritize comprehensive, authoritative content\n\n    3. **Content Extraction & Analysis**:\n       - Extract key content, structure, and insights from each of the top 5 pages\n       - Analyze their writing patterns, information depth, and approach\n       - Identify their strengths and weaknesses\n       - Note their content structure and organization methods\n       - Document their key points and supporting evidence\n\n    **PHASE 2: COMPETITIVE ANALYSIS**\n    1. **Content Gap Identification**: Find what's missing in the top 5 pages\n    2. **Quality Assessment**: Evaluate the depth and accuracy of existing content\n    3. **Structure Analysis**: Understand how top pages organize information\n    4. **Engagement Evaluation**: Assess how well they engage readers\n    5. **Authority Assessment**: Determine their credibility and source quality\n\n    **PHASE 3: SUPERIOR CONTENT CREATION**\n    1. **Enhanced Information**: Include comprehensive data from all previous research phases\n    2. **Improved Structure**: Create better organization than top 5 pages\n    3. **Advanced Engagement**: Implement superior engagement techniques\n    4. **Comprehensive Coverage**: Fill all gaps found in competitor analysis\n    5. **Modern Enhancement**: Add 2025 trends and cutting-edge insights\n    6. **Practical Value**: Provide more actionable and valuable content\n\n    **PHASE 4: ADVANCED CONTENT CREATION**\n    1. **Keyword-Optimized Structure**: Organize content around the extracted keyword naturally\n    2. **Comprehensive Information Integration**: Combine insights from:\n       - All previous research phases (topic analysis, primary research, deep research)\n       - Top 5 page content analysis\n       - Gap analysis findings\n       - Content strategy recommendations\n    3. **Superior Writing Implementation**:\n       - Create hooks that surpass competitor introductions\n       - Use transition techniques better than top articles\n       - Establish stronger credibility than competitors\n       - Implement psychological triggers for reader retention\n       - Pack more useful information per paragraph\n       - Optimize for better reading experience\n       - Create stronger emotional connections\n       - Provide more actionable insights\n\n    **CONTENT OPTIMIZATION STRATEGY:**\n    - Use the extracted keyword naturally throughout the content\n    - Exceed top 5 pages in information depth and quality\n    - Include data and insights they missed\n    - Provide better examples and explanations\n    - Create superior structure and organization\n    - Develop more compelling conclusions and CTAs\n\n    **QUALITY STANDARDS:**\n    - Must be more informative than the top 5 ranking pages\n    - Must be more engaging than current top content\n    - Must provide more practical value than competitors\n    - Must have superior readability and flow\n    - Must establish stronger authority through better sources\n    - Must include more current and relevant information (2025 trends)\n\n    **Content Guidelines:**\n    - Follow the target word count: {wordCount}\n    - Use the specified tone: {tone}\n    - Address the target audience: {targetAudience}\n    - Include 2025 trends and current year information\n    - Reference authoritative sources from all research phases\n    - Create paragraph-sized data points for better context\n    - Strictly adhere to the exact topic without deviation\n    - Naturally incorporate the extracted keyword for SEO optimization\n\n    **EXECUTION WORKFLOW:**\n    1. **Step 1**: Extract the single most important keyword from the topic\n    2. **Step 2**: Search for top 5 pages using that exact keyword\n    3. **Step 3**: Extract and analyze content from those 5 pages\n    4. **Step 4**: Combine competitor insights with comprehensive research data\n    5. **Step 5**: Create superior content that outranks all existing articles\n\n    **Tool Usage:**\n    Use Tavily search strategically with the correct parameter name. IMPORTANT: Use \"searchQuery\" (not \"query\"):\n    1. Search using the extracted keyword: {\"searchQuery\": \"extracted keyword\"}\n    2. Extract comprehensive content from each page\n    3. Analyze their approaches and identify improvement opportunities\n\n    **If search tools encounter issues:**\n    - Use your knowledge of the topic and keyword optimization\n    - Apply proven content creation techniques\n    - Focus on creating comprehensive, engaging content using research data\n    - Include recommendations for manual competitor verification\n\n    **Input:** Use all previous task results including research findings, content strategy, gap analysis, and deep research, plus the extracted keyword and top 5 page analysis.`,\n    expectedOutput: `Superior content created through keyword-focused competitive analysis including:\n    - Extracted primary keyword from the topic with justification\n    - Analysis of top 5 ranking pages for the extracted keyword\n    - Content extraction and competitive insights from each page\n    - Comprehensive comparison of competitor strengths and weaknesses\n    - Enhanced content structure that surpasses top 5 pages\n    - Complete article that outranks all existing content for the keyword\n    - Superior information density using all research phases\n    - Better organization and flow than top competitors\n    - Stronger authority building through comprehensive source integration\n    - More practical value and actionable insights than competitors\n    - Strategic keyword optimization that feels natural and effective\n    - Content that meets all specified requirements while dominating the keyword landscape`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[5]\n});\nlogKaibanTask('Enhanced content generation task created with keyword extraction and top 5 page analysis');\n/**\n * Export all tasks in execution order\n */ const superAgentTasks = [\n    topicAnalysisTask,\n    contentStrategyTask,\n    primaryResearchTask,\n    gapAnalysisTask,\n    deepResearchTask,\n    contentGenerationTask\n];\n/**\n * Task configuration for easy reference\n */ const taskConfig = {\n    tasks: superAgentTasks,\n    taskNames: [\n        'Topic Analysis',\n        'Content Strategy',\n        'Primary Research',\n        'Gap Analysis',\n        'Deep Research',\n        'Content Generation'\n    ],\n    totalTasks: superAgentTasks.length,\n    phaseDistribution: {\n        qwen: [\n            'Topic Analysis',\n            'Gap Analysis'\n        ],\n        gemini: [\n            'Content Strategy',\n            'Primary Research',\n            'Deep Research',\n            'Content Generation'\n        ]\n    },\n    tasksWithTools: [\n        'Topic Analysis',\n        'Primary Research',\n        'Deep Research',\n        'Content Generation'\n    ]\n};\nlogKaibanTask('KaibanJS Task System initialization complete', {\n    status: 'ready',\n    totalTasks: 6,\n    sequentialExecution: true,\n    taskDependencies: {\n        'Content Strategy': [\n            'Topic Analysis'\n        ],\n        'Primary Research': [\n            'Topic Analysis',\n            'Content Strategy'\n        ],\n        'Gap Analysis': [\n            'Topic Analysis',\n            'Content Strategy',\n            'Primary Research'\n        ],\n        'Deep Research': [\n            'Topic Analysis',\n            'Content Strategy',\n            'Primary Research',\n            'Gap Analysis'\n        ],\n        'Content Generation': [\n            'All Previous Tasks'\n        ]\n    },\n    timestamp: new Date().toISOString()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/tasks.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/tools.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kaibanTools: () => (/* binding */ kaibanTools),\n/* harmony export */   tavilySearchTool: () => (/* binding */ tavilySearchTool),\n/* harmony export */   validateTavilyTool: () => (/* binding */ validateTavilyTool)\n/* harmony export */ });\n/* harmony import */ var _kaibanjs_tools__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @kaibanjs/tools */ \"(rsc)/./node_modules/@kaibanjs/tools/dist/index.esm.js\");\n/**\n * KaibanJS Tools Configuration\n *\n * This file configures the Tavily search tool for KaibanJS agents\n * following the official KaibanJS documentation pattern.\n */ \n// Console logging utility for Kaiban Tools\nconst logKaibanTool = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🔧 [KAIBAN-TOOL] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-TOOL-DATA]:`, data);\n    }\n};\n/**\n * Tavily Search Tool Configuration\n *\n * Following the official KaibanJS Tavily search tool implementation pattern:\n * - Import TavilySearchResults from @kaibanjs/tools\n * - Instantiate with apiKey and maxResults parameters\n * - Add to agent's tools array\n */ logKaibanTool('Initializing Tavily Search Tool');\nconst tavilySearchTool = new _kaibanjs_tools__WEBPACK_IMPORTED_MODULE_0__.TavilySearchResults({\n    apiKey: \"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\" || 0,\n    maxResults: 10\n});\nlogKaibanTool('Tavily Search Tool configured', {\n    maxResults: 10,\n    apiKeyConfigured: !!\"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\",\n    toolType: 'KaibanJS TavilySearchResults'\n});\n/**\n * Tool validation function\n */ const validateTavilyTool = ()=>{\n    const isValid = !!\"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\";\n    if (!isValid) {\n        logKaibanTool('⚠️ Tavily API key not configured - workflow will continue without search', {\n            error: 'TAVILY_API_KEY environment variable is missing',\n            solution: 'Add your Tavily API key to the .env.local file',\n            documentation: 'https://docs.kaibanjs.com/tools-docs/kaibanjs-tools/Tavily',\n            impact: 'Search functionality will be limited, but workflow can continue'\n        });\n    } else {\n        logKaibanTool('✅ Tavily tool validation passed');\n    }\n    return isValid;\n};\n/**\n * Export tools array for easy import in agents\n */ const kaibanTools = [\n    tavilySearchTool\n];\nlogKaibanTool('KaibanJS Tools initialization complete', {\n    totalTools: kaibanTools.length,\n    toolNames: [\n        'TavilySearchResults'\n    ],\n    toolSource: '@kaibanjs/tools',\n    status: 'ready'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/kaibanjs","vendor-chunks/@kaibanjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();